# 鼠标控制工具

这是一个全局鼠标控制工具，可以在浏览器或任何应用程序中使用快捷键来控制鼠标移动和点击。

## 功能特性

- 🖱️ **全局快捷键控制**: 在任何应用程序中都可以使用
- 🎯 **精确移动**: 每次移动1像素，精确控制
- 🔄 **持续移动**: 按住快捷键可以持续移动
- 🖱️ **自动点击**: 每次移动前自动点击鼠标左键

## 快捷键说明

| 快捷键 | 功能 |
|--------|------|
| `Ctrl + ↑` | 点击鼠标左键并向上移动 |
| `Ctrl + ↓` | 点击鼠标左键并向下移动 |
| `Ctrl + ←` | 点击鼠标左键并向左移动 |
| `Ctrl + →` | 点击鼠标左键并向右移动 |
| `ESC` | 退出程序 |

## 安装依赖

```bash
pip install -r requirements.txt
```

或者单独安装：

```bash
pip install pynput==1.7.6
```

## 使用方法

1. **启动程序**:
   ```bash
   python mouse_controller.py
   ```

2. **使用快捷键**:
   - 程序启动后会在后台运行
   - 在任何应用程序中（包括浏览器）使用 `Ctrl + 方向键` 来控制鼠标
   - 按住快捷键可以持续移动鼠标

3. **退出程序**:
   - 按 `ESC` 键退出
   - 或者在终端中按 `Ctrl + C`

## 工作原理

1. **全局监听**: 程序使用 `pynput` 库监听全局键盘事件
2. **快捷键检测**: 检测 `Ctrl + 方向键` 组合
3. **鼠标操作**: 
   - 首先点击鼠标左键
   - 然后开始向指定方向移动鼠标
4. **持续移动**: 使用多线程实现按住快捷键时的持续移动
5. **停止移动**: 释放快捷键时停止移动

## 注意事项

⚠️ **重要提醒**:
- 程序需要管理员权限才能监听全局键盘事件
- 在某些安全软件环境下可能需要添加信任
- 使用时请确保不会干扰其他应用程序的正常快捷键功能

## 技术细节

- **语言**: Python 3.x
- **主要依赖**: pynput
- **移动精度**: 1像素/次
- **移动频率**: 100Hz (每10毫秒移动一次)
- **多线程**: 支持多方向同时移动

## 故障排除

1. **权限问题**: 
   - Windows: 以管理员身份运行
   - macOS: 在系统偏好设置中授予辅助功能权限
   - Linux: 确保用户在input组中

2. **快捷键冲突**:
   - 如果与其他程序快捷键冲突，可以修改代码中的快捷键组合

3. **移动速度调整**:
   - 修改 `time.sleep(0.01)` 中的值来调整移动速度
   - 值越小移动越快，值越大移动越慢
