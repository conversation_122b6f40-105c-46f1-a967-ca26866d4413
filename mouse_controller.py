import time
import threading
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
from pynput.keyboard import Key, Listener as KeyboardListener
import sys

class MouseController:
    def __init__(self):
        self.mouse_controller = mouse.Controller()
        self.is_running = True
        self.movement_threads = {}
        self.pressed_keys = set()
        self.mouse_pressed = False
        
    def move_mouse(self, dx, dy):
        """移动鼠标指定像素"""
        current_pos = self.mouse_controller.position
        new_x = current_pos[0] + dx
        new_y = current_pos[1] + dy
        self.mouse_controller.position = (new_x, new_y)
        
    def press_and_move(self, direction):
        """按住鼠标左键并开始移动"""
        # 如果鼠标还没有按下，则按下鼠标左键
        if not self.mouse_pressed:
            self.mouse_controller.press(Button.left)
            self.mouse_pressed = True
            print("鼠标左键按下")

        # 根据方向设置移动增量
        movement_map = {
            'up': (0, -1),
            'down': (0, 1),
            'left': (-1, 0),
            'right': (1, 0)
        }

        dx, dy = movement_map.get(direction, (0, 0))

        # 开始持续移动
        def continuous_move():
            while direction in self.movement_threads and self.is_running:
                self.move_mouse(dx, dy)
                time.sleep(0.01)  # 控制移动速度，每10毫秒移动一次

        # 停止之前的移动线程（如果存在）
        if direction in self.movement_threads:
            del self.movement_threads[direction]

        # 启动新的移动线程
        thread = threading.Thread(target=continuous_move, daemon=True)
        self.movement_threads[direction] = thread
        thread.start()
        
    def stop_movement(self, direction):
        """停止指定方向的移动"""
        if direction in self.movement_threads:
            del self.movement_threads[direction]

        # 如果所有方向都停止了，释放鼠标左键
        if not self.movement_threads and self.mouse_pressed:
            self.mouse_controller.release(Button.left)
            self.mouse_pressed = False
            print("鼠标左键释放")
            
    def on_key_press(self, key):
        """按键按下事件处理"""
        try:
            # 检查是否按下了Ctrl键
            if key == Key.ctrl_l or key == Key.ctrl_r:
                self.pressed_keys.add('ctrl')
            elif hasattr(key, 'name'):
                self.pressed_keys.add(key.name)
            
            # 检查Ctrl+方向键组合
            if 'ctrl' in self.pressed_keys:
                if key == Key.up:
                    self.pressed_keys.add('up')
                    self.press_and_move('up')
                    print("Ctrl+↑: 按住鼠标并向上移动")
                elif key == Key.down:
                    self.pressed_keys.add('down')
                    self.press_and_move('down')
                    print("Ctrl+↓: 按住鼠标并向下移动")
                elif key == Key.left:
                    self.pressed_keys.add('left')
                    self.press_and_move('left')
                    print("Ctrl+←: 按住鼠标并向左移动")
                elif key == Key.right:
                    self.pressed_keys.add('right')
                    self.press_and_move('right')
                    print("Ctrl+→: 按住鼠标并向右移动")
                    
        except AttributeError:
            pass
            
    def on_key_release(self, key):
        """按键释放事件处理"""
        try:
            # 移除释放的按键
            if key == Key.ctrl_l or key == Key.ctrl_r:
                self.pressed_keys.discard('ctrl')
            elif hasattr(key, 'name'):
                self.pressed_keys.discard(key.name)
                
            # 停止对应方向的移动
            if key == Key.up:
                self.pressed_keys.discard('up')
                self.stop_movement('up')
            elif key == Key.down:
                self.pressed_keys.discard('down')
                self.stop_movement('down')
            elif key == Key.left:
                self.pressed_keys.discard('left')
                self.stop_movement('left')
            elif key == Key.right:
                self.pressed_keys.discard('right')
                self.stop_movement('right')
                
            # 检查退出快捷键 Ctrl+Shift+Q
            if key == Key.esc:
                print("按下ESC键，程序退出")
                self.stop()
                return False
                
        except AttributeError:
            pass
            
    def start(self):
        """启动监听器"""
        print("鼠标控制工具已启动!")
        print("使用方法:")
        print("- Ctrl+↑: 按住鼠标左键并向上拖拽")
        print("- Ctrl+↓: 按住鼠标左键并向下拖拽")
        print("- Ctrl+←: 按住鼠标左键并向左拖拽")
        print("- Ctrl+→: 按住鼠标左键并向右拖拽")
        print("- 按住快捷键可持续拖拽移动")
        print("- 释放快捷键自动释放鼠标")
        print("- 按ESC键退出程序")
        print("-" * 50)
        
        # 启动键盘监听器
        with KeyboardListener(
            on_press=self.on_key_press,
            on_release=self.on_key_release) as listener:
            try:
                listener.join()
            except KeyboardInterrupt:
                self.stop()
                
    def stop(self):
        """停止程序"""
        self.is_running = False
        self.movement_threads.clear()
        print("程序已停止")

def main():
    print("正在启动鼠标控制工具...")
    try:
        controller = MouseController()
        controller.start()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        print("可能的解决方案:")
        print("1. 以管理员身份运行程序")
        print("2. 检查是否安装了pynput库: pip install pynput")
        print("3. 在某些系统上可能需要授予辅助功能权限")
        import traceback
        traceback.print_exc()
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()
