<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鼠标拖拽测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .drag-box {
            width: 150px;
            height: 150px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: 3px solid #333;
            border-radius: 10px;
            margin: 20px;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            position: relative;
            transition: transform 0.1s ease;
        }
        
        .drag-box:hover {
            transform: scale(1.05);
        }
        
        .drag-box:active {
            transform: scale(0.95);
        }
        
        .test-area {
            border: 2px dashed #ccc;
            min-height: 400px;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
            position: relative;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        }
        
        .selectable-text {
            background: #fff3cd;
            padding: 10px;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            margin: 10px 0;
            user-select: text;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ 鼠标拖拽测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li><strong>拖拽测试</strong>：将鼠标放在下面的彩色方框上，使用 <code>Ctrl + 方向键</code> 进行拖拽</li>
                <li><strong>文本选择</strong>：在文本区域使用快捷键选择文本</li>
                <li><strong>观察状态</strong>：右上角会显示鼠标位置和状态</li>
            </ul>
        </div>
        
        <div class="test-area">
            <div class="drag-box" draggable="true">
                拖拽我！<br>
                🎯
            </div>
            
            <div class="selectable-text">
                这是一段可选择的文本。您可以将鼠标放在这里，然后使用 Ctrl + 方向键来选择文本。
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </div>
            
            <div class="selectable-text">
                另一段测试文本。尝试从这里开始拖拽选择，看看程序是否能正确模拟鼠标拖拽操作。
                这个测试可以帮助验证程序的拖拽功能是否正常工作。
            </div>
        </div>
    </div>
    
    <div class="status" id="status">
        鼠标位置: (0, 0)<br>
        状态: 准备就绪
    </div>
    
    <script>
        // 实时显示鼠标位置
        document.addEventListener('mousemove', function(e) {
            const status = document.getElementById('status');
            status.innerHTML = `
                鼠标位置: (${e.clientX}, ${e.clientY})<br>
                页面坐标: (${e.pageX}, ${e.pageY})<br>
                状态: 移动中
            `;
        });
        
        // 监听鼠标按下和释放
        document.addEventListener('mousedown', function(e) {
            const status = document.getElementById('status');
            status.innerHTML += '<br>🖱️ 鼠标按下';
        });
        
        document.addEventListener('mouseup', function(e) {
            const status = document.getElementById('status');
            status.innerHTML += '<br>🖱️ 鼠标释放';
        });
        
        // 拖拽事件监听
        const dragBox = document.querySelector('.drag-box');
        
        dragBox.addEventListener('dragstart', function(e) {
            console.log('开始拖拽');
            this.style.opacity = '0.5';
        });
        
        dragBox.addEventListener('dragend', function(e) {
            console.log('拖拽结束');
            this.style.opacity = '1';
        });
        
        // 文本选择监听
        document.addEventListener('selectstart', function(e) {
            console.log('开始选择文本');
        });
        
        document.addEventListener('selectionchange', function(e) {
            const selection = window.getSelection();
            if (selection.toString().length > 0) {
                console.log('选择了文本:', selection.toString().substring(0, 50) + '...');
            }
        });
    </script>
</body>
</html>
